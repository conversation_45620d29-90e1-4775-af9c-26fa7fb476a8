#!/usr/bin/env python3
"""
AI Anomaly Detection Service using Nixtla/TimeGPT
Provides REST API endpoints for anomaly detection on API monitoring data
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import json
import logging
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from nixtla import NixtlaClient
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="PulseMesh AI Anomaly Detection Service",
    description="AI-powered anomaly detection for API monitoring data using Nixtla/TimeGPT",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Nixtla client
NIXTLA_API_KEY = os.getenv("NIXTLA_API_KEY")
if not NIXTLA_API_KEY:
    raise ValueError("NIXTLA_API_KEY environment variable is required")

nixtla_client = NixtlaClient(api_key=NIXTLA_API_KEY)

# Pydantic models
class HealthCheckData(BaseModel):
    timestamp: int
    latency: float
    is_healthy: bool
    response_time: float
    error_rate: Optional[float] = 0.0

class AnomalyDetectionRequest(BaseModel):
    provider_id: str
    data: List[HealthCheckData]
    time_column: str = "timestamp"
    target_column: str = "latency"
    frequency: str = "T"  # Minute frequency
    level: List[int] = Field(default=[80, 90, 99])  # Confidence levels

class AnomalyResult(BaseModel):
    timestamp: int
    value: float
    is_anomaly: bool
    anomaly_score: float
    confidence_level: int
    upper_bound: Optional[float] = None
    lower_bound: Optional[float] = None

class AnomalyDetectionResponse(BaseModel):
    provider_id: str
    total_points: int
    anomalies_detected: int
    anomaly_rate: float
    results: List[AnomalyResult]
    summary: Dict[str, Any]

class ForecastRequest(BaseModel):
    provider_id: str
    data: List[HealthCheckData]
    horizon: int = 60  # Forecast 60 minutes ahead
    time_column: str = "timestamp"
    target_column: str = "latency"
    frequency: str = "T"

class ForecastResult(BaseModel):
    timestamp: int
    predicted_value: float
    confidence_interval_lower: float
    confidence_interval_upper: float

class ForecastResponse(BaseModel):
    provider_id: str
    forecast_horizon: int
    results: List[ForecastResult]

# Utility functions
def prepare_dataframe(data: List[HealthCheckData], time_col: str, target_col: str) -> pd.DataFrame:
    """Convert health check data to pandas DataFrame for Nixtla processing"""
    df_data = []
    for item in data:
        row = {
            'unique_id': 'api_metrics',  # Required by Nixtla
            time_col: pd.to_datetime(item.timestamp, unit='ms'),
            target_col: getattr(item, target_col.replace('_', '')),  # Handle snake_case to camelCase
        }
        # Add additional metrics
        if target_col != 'latency':
            row['latency'] = item.latency
        if target_col != 'response_time':
            row['response_time'] = item.response_time
        if target_col != 'error_rate':
            row['error_rate'] = item.error_rate or 0.0
        
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    df = df.sort_values(time_col).reset_index(drop=True)
    
    # Ensure minimum data points for analysis
    if len(df) < 10:
        raise ValueError("Insufficient data points for anomaly detection (minimum 10 required)")
    
    return df

def calculate_anomaly_score(value: float, lower_bound: float, upper_bound: float) -> float:
    """Calculate anomaly score based on how far the value is from normal bounds"""
    if lower_bound <= value <= upper_bound:
        return 0.0
    
    # Calculate distance from nearest bound
    if value < lower_bound:
        distance = lower_bound - value
        range_size = upper_bound - lower_bound
    else:
        distance = value - upper_bound
        range_size = upper_bound - lower_bound
    
    # Normalize score (0-1 scale)
    if range_size > 0:
        score = min(distance / range_size, 1.0)
    else:
        score = 1.0 if distance > 0 else 0.0
    
    return score

# API Endpoints
@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "service": "PulseMesh AI Anomaly Detection",
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    try:
        # Test Nixtla client
        test_df = pd.DataFrame({
            'unique_id': ['test'],
            'ds': [pd.Timestamp.now()],
            'y': [1.0]
        })
        # This is a simple test - in production you might want a more comprehensive check
        
        return {
            "status": "healthy",
            "nixtla_client": "connected",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

@app.post("/detect-anomalies", response_model=AnomalyDetectionResponse)
async def detect_anomalies(request: AnomalyDetectionRequest):
    """Detect anomalies in API monitoring data"""
    try:
        logger.info(f"Starting anomaly detection for provider {request.provider_id}")
        
        # Prepare data
        df = prepare_dataframe(request.data, request.time_column, request.target_column)
        logger.info(f"Prepared DataFrame with {len(df)} data points")
        
        # Perform anomaly detection
        anomalies_df = nixtla_client.detect_anomalies(
            df,
            time_col=request.time_column,
            target_col=request.target_column,
            freq=request.frequency,
            level=request.level
        )
        
        # Process results
        results = []
        anomaly_count = 0
        
        for _, row in anomalies_df.iterrows():
            timestamp_ms = int(row[request.time_column].timestamp() * 1000)
            value = float(row[request.target_column])
            
            # Check if it's an anomaly (Nixtla marks anomalies in specific columns)
            is_anomaly = False
            anomaly_score = 0.0
            upper_bound = None
            lower_bound = None
            confidence_level = 80
            
            # Look for anomaly indicators in the DataFrame
            for level in request.level:
                upper_col = f"{request.target_column}-hi-{level}"
                lower_col = f"{request.target_column}-lo-{level}"
                
                if upper_col in row and lower_col in row:
                    upper_bound = float(row[upper_col]) if pd.notna(row[upper_col]) else None
                    lower_bound = float(row[lower_col]) if pd.notna(row[lower_col]) else None
                    
                    if upper_bound is not None and lower_bound is not None:
                        if value < lower_bound or value > upper_bound:
                            is_anomaly = True
                            anomaly_score = calculate_anomaly_score(value, lower_bound, upper_bound)
                            confidence_level = level
                            break
            
            if is_anomaly:
                anomaly_count += 1
            
            results.append(AnomalyResult(
                timestamp=timestamp_ms,
                value=value,
                is_anomaly=is_anomaly,
                anomaly_score=anomaly_score,
                confidence_level=confidence_level,
                upper_bound=upper_bound,
                lower_bound=lower_bound
            ))
        
        # Calculate summary statistics
        total_points = len(results)
        anomaly_rate = (anomaly_count / total_points) * 100 if total_points > 0 else 0
        
        summary = {
            "analysis_period": {
                "start": int(df[request.time_column].min().timestamp() * 1000),
                "end": int(df[request.time_column].max().timestamp() * 1000)
            },
            "data_quality": {
                "total_points": total_points,
                "missing_points": 0,  # Could be enhanced to detect gaps
                "data_completeness": 100.0
            },
            "anomaly_statistics": {
                "total_anomalies": anomaly_count,
                "anomaly_rate_percent": round(anomaly_rate, 2),
                "severity_distribution": {
                    "high": len([r for r in results if r.is_anomaly and r.anomaly_score > 0.7]),
                    "medium": len([r for r in results if r.is_anomaly and 0.3 < r.anomaly_score <= 0.7]),
                    "low": len([r for r in results if r.is_anomaly and r.anomaly_score <= 0.3])
                }
            }
        }
        
        logger.info(f"Anomaly detection completed: {anomaly_count}/{total_points} anomalies detected")
        
        return AnomalyDetectionResponse(
            provider_id=request.provider_id,
            total_points=total_points,
            anomalies_detected=anomaly_count,
            anomaly_rate=anomaly_rate,
            results=results,
            summary=summary
        )
        
    except Exception as e:
        logger.error(f"Anomaly detection failed: {e}")
        raise HTTPException(status_code=500, detail=f"Anomaly detection failed: {str(e)}")

@app.post("/forecast", response_model=ForecastResponse)
async def forecast_metrics(request: ForecastRequest):
    """Generate forecasts for API metrics"""
    try:
        logger.info(f"Starting forecast for provider {request.provider_id}")
        
        # Prepare data
        df = prepare_dataframe(request.data, request.time_column, request.target_column)
        logger.info(f"Prepared DataFrame with {len(df)} data points for forecasting")
        
        # Generate forecast
        forecast_df = nixtla_client.forecast(
            df,
            h=request.horizon,
            time_col=request.time_column,
            target_col=request.target_column,
            freq=request.frequency,
            level=[80, 90]  # Confidence intervals
        )
        
        # Process forecast results
        results = []
        for _, row in forecast_df.iterrows():
            timestamp_ms = int(row[request.time_column].timestamp() * 1000)
            
            results.append(ForecastResult(
                timestamp=timestamp_ms,
                predicted_value=float(row[request.target_column]),
                confidence_interval_lower=float(row[f"{request.target_column}-lo-80"]) if f"{request.target_column}-lo-80" in row else 0.0,
                confidence_interval_upper=float(row[f"{request.target_column}-hi-80"]) if f"{request.target_column}-hi-80" in row else 0.0
            ))
        
        logger.info(f"Forecast completed: {len(results)} future points generated")
        
        return ForecastResponse(
            provider_id=request.provider_id,
            forecast_horizon=request.horizon,
            results=results
        )
        
    except Exception as e:
        logger.error(f"Forecasting failed: {e}")
        raise HTTPException(status_code=500, detail=f"Forecasting failed: {str(e)}")

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
