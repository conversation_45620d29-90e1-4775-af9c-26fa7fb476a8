#!/usr/bin/env python3
"""
Setup script for PulseMesh AI Anomaly Detection Service
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up PulseMesh AI Anomaly Detection Service")
    
    # Check if Python is available
    try:
        python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
        print(f"✅ Python found: {python_version}")
    except Exception as e:
        print(f"❌ Python not found: {e}")
        return False
    
    # Create virtual environment
    venv_path = Path("venv")
    if not venv_path.exists():
        if not run_command(f"{sys.executable} -m venv venv", "Creating virtual environment"):
            return False
    else:
        print("✅ Virtual environment already exists")
    
    # Determine activation script based on OS
    if os.name == 'nt':  # Windows
        activate_script = "venv\\Scripts\\activate"
        pip_command = "venv\\Scripts\\pip"
        python_command = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        activate_script = "source venv/bin/activate"
        pip_command = "venv/bin/pip"
        python_command = "venv/bin/python"
    
    # Install requirements
    if not run_command(f"{pip_command} install --upgrade pip", "Upgrading pip"):
        return False
    
    if not run_command(f"{pip_command} install -r requirements.txt", "Installing Python dependencies"):
        return False
    
    # Create .env file if it doesn't exist
    env_file = Path(".env")
    if not env_file.exists():
        env_content = """# PulseMesh AI Anomaly Detection Service Configuration
NIXTLA_API_KEY=your_nixtla_api_key_here
PORT=8000
LOG_LEVEL=INFO
"""
        with open(env_file, "w") as f:
            f.write(env_content)
        print("✅ Created .env file template")
        print("⚠️  Please update the NIXTLA_API_KEY in the .env file")
    else:
        print("✅ .env file already exists")
    
    # Test the installation
    print("\n🧪 Testing installation...")
    test_command = f"{python_command} -c \"import nixtla, pandas, fastapi; print('All packages imported successfully')\""
    if run_command(test_command, "Testing package imports"):
        print("\n🎉 Setup completed successfully!")
        print("\nTo start the service:")
        if os.name == 'nt':
            print("1. Activate virtual environment: venv\\Scripts\\activate")
        else:
            print("1. Activate virtual environment: source venv/bin/activate")
        print("2. Update NIXTLA_API_KEY in .env file")
        print("3. Run the service: python anomaly_detection_service.py")
        return True
    else:
        print("❌ Installation test failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
