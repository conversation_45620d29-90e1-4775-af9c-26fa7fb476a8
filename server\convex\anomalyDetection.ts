import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Create a new anomaly detection analysis record
export const createAnalysis = mutation({
  args: {
    analysisId: v.string(),
    providerId: v.id("apiProviders"),
    userId: v.string(),
    startTimestamp: v.number(),
    endTimestamp: v.number(),
    analysisTimestamp: v.number(),
    metricType: v.string(),
    totalDataPoints: v.number(),
    anomaliesDetected: v.number(),
    anomalyRate: v.number(),
    confidenceLevels: v.array(v.number()),
    status: v.string(),
    errorMessage: v.optional(v.string()),
    summary: v.object({
      dataQuality: v.object({
        totalPoints: v.number(),
        missingPoints: v.number(),
        dataCompleteness: v.number(),
      }),
      anomalyStatistics: v.object({
        totalAnomalies: v.number(),
        anomalyRatePercent: v.number(),
        severityDistribution: v.object({
          high: v.number(),
          medium: v.number(),
          low: v.number(),
        }),
      }),
    }),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("anomalyDetectionAnalyses", args);
  },
});

// Create anomaly detection results (batch insert)
export const createAnomalyResults = mutation({
  args: {
    results: v.array(v.object({
      providerId: v.id("apiProviders"),
      analysisId: v.string(),
      timestamp: v.number(),
      dataPointTimestamp: v.number(),
      value: v.number(),
      metricType: v.string(),
      isAnomaly: v.boolean(),
      anomalyScore: v.number(),
      confidenceLevel: v.number(),
      upperBound: v.optional(v.number()),
      lowerBound: v.optional(v.number()),
      severity: v.string(),
      userId: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const insertPromises = args.results.map(result => 
      ctx.db.insert("anomalyDetectionResults", result)
    );
    return await Promise.all(insertPromises);
  },
});

// Get anomaly detection analyses for a provider
export const getAnalysesByProvider = query({
  args: { 
    providerId: v.id("apiProviders"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    return await ctx.db
      .query("anomalyDetectionAnalyses")
      .filter((q) => q.eq(q.field("providerId"), args.providerId))
      .order("desc")
      .take(limit);
  },
});

// Get anomaly detection analyses for a user
export const getAnalysesByUser = query({
  args: { 
    userId: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    return await ctx.db
      .query("anomalyDetectionAnalyses")
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .order("desc")
      .take(limit);
  },
});

// Get anomaly results for a specific analysis
export const getAnomalyResults = query({
  args: { 
    analysisId: v.string(),
    anomaliesOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("anomalyDetectionResults")
      .filter((q) => q.eq(q.field("analysisId"), args.analysisId));
    
    if (args.anomaliesOnly) {
      query = query.filter((q) => q.eq(q.field("isAnomaly"), true));
    }
    
    return await query.order("asc").collect();
  },
});

// Get recent anomalies for a provider
export const getRecentAnomalies = query({
  args: { 
    providerId: v.id("apiProviders"),
    since: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;
    let query = ctx.db
      .query("anomalyDetectionResults")
      .filter((q) => q.eq(q.field("providerId"), args.providerId))
      .filter((q) => q.eq(q.field("isAnomaly"), true));
    
    if (args.since) {
      query = query.filter((q) => q.gte(q.field("timestamp"), args.since));
    }
    
    return await query.order("desc").take(limit);
  },
});

// Create forecast results (batch insert)
export const createForecastResults = mutation({
  args: {
    results: v.array(v.object({
      providerId: v.id("apiProviders"),
      forecastId: v.string(),
      generatedTimestamp: v.number(),
      forecastTimestamp: v.number(),
      metricType: v.string(),
      predictedValue: v.number(),
      confidenceIntervalLower: v.number(),
      confidenceIntervalUpper: v.number(),
      horizon: v.number(),
      userId: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const insertPromises = args.results.map(result => 
      ctx.db.insert("forecastResults", result)
    );
    return await Promise.all(insertPromises);
  },
});

// Get forecast results for a provider
export const getForecastsByProvider = query({
  args: { 
    providerId: v.id("apiProviders"),
    metricType: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;
    let query = ctx.db
      .query("forecastResults")
      .filter((q) => q.eq(q.field("providerId"), args.providerId));
    
    if (args.metricType) {
      query = query.filter((q) => q.eq(q.field("metricType"), args.metricType));
    }
    
    return await query.order("desc").take(limit);
  },
});

// Get or create anomaly detection settings for a user/provider
export const getSettings = query({
  args: { 
    userId: v.string(),
    providerId: v.optional(v.id("apiProviders")),
  },
  handler: async (ctx, args) => {
    const settings = await ctx.db
      .query("anomalyDetectionSettings")
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .filter((q) => 
        args.providerId 
          ? q.eq(q.field("providerId"), args.providerId)
          : q.eq(q.field("providerId"), undefined)
      )
      .first();
    
    return settings;
  },
});

// Create or update anomaly detection settings
export const upsertSettings = mutation({
  args: {
    userId: v.string(),
    providerId: v.optional(v.id("apiProviders")),
    isEnabled: v.boolean(),
    autoAnalysisEnabled: v.boolean(),
    analysisFrequency: v.number(),
    minDataPoints: v.number(),
    confidenceLevels: v.array(v.number()),
    metricTypes: v.array(v.string()),
    alertThresholds: v.object({
      anomalyRatePercent: v.number(),
      severityLevel: v.string(),
    }),
    retentionDays: v.number(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("anomalyDetectionSettings")
      .filter((q) => q.eq(q.field("userId"), args.userId))
      .filter((q) => 
        args.providerId 
          ? q.eq(q.field("providerId"), args.providerId)
          : q.eq(q.field("providerId"), undefined)
      )
      .first();
    
    const now = Date.now();
    
    if (existing) {
      return await ctx.db.patch(existing._id, {
        ...args,
        updatedAt: now,
      });
    } else {
      return await ctx.db.insert("anomalyDetectionSettings", {
        ...args,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Get anomaly statistics for a provider
export const getAnomalyStats = query({
  args: { 
    providerId: v.id("apiProviders"),
    since: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("anomalyDetectionResults")
      .filter((q) => q.eq(q.field("providerId"), args.providerId));
    
    if (args.since) {
      query = query.filter((q) => q.gte(q.field("timestamp"), args.since));
    }
    
    const results = await query.collect();
    
    if (results.length === 0) {
      return {
        totalDataPoints: 0,
        totalAnomalies: 0,
        anomalyRate: 0,
        severityDistribution: { high: 0, medium: 0, low: 0 },
        metricBreakdown: {},
      };
    }
    
    const anomalies = results.filter(r => r.isAnomaly);
    const severityDistribution = {
      high: anomalies.filter(a => a.severity === 'high').length,
      medium: anomalies.filter(a => a.severity === 'medium').length,
      low: anomalies.filter(a => a.severity === 'low').length,
    };
    
    // Group by metric type
    const metricBreakdown: Record<string, any> = {};
    for (const result of results) {
      if (!metricBreakdown[result.metricType]) {
        metricBreakdown[result.metricType] = {
          total: 0,
          anomalies: 0,
          anomalyRate: 0,
        };
      }
      metricBreakdown[result.metricType].total++;
      if (result.isAnomaly) {
        metricBreakdown[result.metricType].anomalies++;
      }
    }
    
    // Calculate anomaly rates for each metric
    for (const metric in metricBreakdown) {
      const data = metricBreakdown[metric];
      data.anomalyRate = data.total > 0 ? (data.anomalies / data.total) * 100 : 0;
    }
    
    return {
      totalDataPoints: results.length,
      totalAnomalies: anomalies.length,
      anomalyRate: (anomalies.length / results.length) * 100,
      severityDistribution,
      metricBreakdown,
    };
  },
});

// Clean up old anomaly detection data
export const cleanupOldData = mutation({
  args: { 
    olderThan: v.number(), // timestamp
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 1000;
    
    // Clean up old results
    const oldResults = await ctx.db
      .query("anomalyDetectionResults")
      .filter((q) => q.lt(q.field("timestamp"), args.olderThan))
      .order("asc")
      .take(limit);
    
    for (const result of oldResults) {
      await ctx.db.delete(result._id);
    }
    
    // Clean up old analyses
    const oldAnalyses = await ctx.db
      .query("anomalyDetectionAnalyses")
      .filter((q) => q.lt(q.field("analysisTimestamp"), args.olderThan))
      .order("asc")
      .take(limit);
    
    for (const analysis of oldAnalyses) {
      await ctx.db.delete(analysis._id);
    }
    
    // Clean up old forecasts
    const oldForecasts = await ctx.db
      .query("forecastResults")
      .filter((q) => q.lt(q.field("generatedTimestamp"), args.olderThan))
      .order("asc")
      .take(limit);
    
    for (const forecast of oldForecasts) {
      await ctx.db.delete(forecast._id);
    }
    
    return {
      deletedResults: oldResults.length,
      deletedAnalyses: oldAnalyses.length,
      deletedForecasts: oldForecasts.length,
    };
  },
});
