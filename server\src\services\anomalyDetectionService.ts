import axios, { AxiosResponse } from 'axios';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { createObjectCsvWriter } from 'csv-writer';
import { env } from '../config/env.js';
import { ApiError } from '../types/index.js';

// Types for anomaly detection
export interface HealthCheckData {
  timestamp: number;
  latency: number;
  is_healthy: boolean;
  response_time: number;
  error_rate?: number;
}

export interface AnomalyResult {
  timestamp: number;
  value: number;
  is_anomaly: boolean;
  anomaly_score: number;
  confidence_level: number;
  upper_bound?: number;
  lower_bound?: number;
}

export interface AnomalyDetectionResponse {
  provider_id: string;
  total_points: number;
  anomalies_detected: number;
  anomaly_rate: number;
  results: AnomalyResult[];
  summary: {
    analysis_period: {
      start: number;
      end: number;
    };
    data_quality: {
      total_points: number;
      missing_points: number;
      data_completeness: number;
    };
    anomaly_statistics: {
      total_anomalies: number;
      anomaly_rate_percent: number;
      severity_distribution: {
        high: number;
        medium: number;
        low: number;
      };
    };
  };
}

export interface ForecastResult {
  timestamp: number;
  predicted_value: number;
  confidence_interval_lower: number;
  confidence_interval_upper: number;
}

export interface ForecastResponse {
  provider_id: string;
  forecast_horizon: number;
  results: ForecastResult[];
}

export interface AnomalyDetectionRequest {
  provider_id: string;
  data: HealthCheckData[];
  time_column?: string;
  target_column?: string;
  frequency?: string;
  level?: number[];
}

export interface ForecastRequest {
  provider_id: string;
  data: HealthCheckData[];
  horizon?: number;
  time_column?: string;
  target_column?: string;
  frequency?: string;
}

export class AnomalyDetectionService {
  private pythonServiceUrl: string;
  private isServiceHealthy: boolean = false;
  private lastHealthCheck: number = 0;
  private healthCheckInterval: number = 30000; // 30 seconds

  constructor() {
    this.pythonServiceUrl = env.PYTHON_ANOMALY_SERVICE_URL || 'http://localhost:8000';
    this.checkServiceHealth();
  }

  /**
   * Check if the Python anomaly detection service is healthy
   */
  private async checkServiceHealth(): Promise<boolean> {
    const now = Date.now();
    
    // Only check health every 30 seconds
    if (now - this.lastHealthCheck < this.healthCheckInterval) {
      return this.isServiceHealthy;
    }

    try {
      const response: AxiosResponse = await axios.get(`${this.pythonServiceUrl}/health`, {
        timeout: 5000,
      });
      
      this.isServiceHealthy = response.status === 200;
      this.lastHealthCheck = now;
      
      if (this.isServiceHealthy) {
        console.log('✅ Python anomaly detection service is healthy');
      }
      
      return this.isServiceHealthy;
    } catch (error: any) {
      this.isServiceHealthy = false;
      this.lastHealthCheck = now;
      console.warn('⚠️ Python anomaly detection service is not available:', error.message);
      return false;
    }
  }

  /**
   * Detect anomalies in health check data
   */
  async detectAnomalies(request: AnomalyDetectionRequest): Promise<AnomalyDetectionResponse> {
    // Check service health first
    const isHealthy = await this.checkServiceHealth();
    if (!isHealthy) {
      throw new ApiError('Anomaly detection service is not available', 503);
    }

    // Validate input data
    if (!request.data || request.data.length < 10) {
      throw new ApiError('Insufficient data points for anomaly detection (minimum 10 required)', 400);
    }

    try {
      console.log(`🔍 Starting anomaly detection for provider ${request.provider_id} with ${request.data.length} data points`);
      
      const response: AxiosResponse<AnomalyDetectionResponse> = await axios.post(
        `${this.pythonServiceUrl}/detect-anomalies`,
        {
          provider_id: request.provider_id,
          data: request.data,
          time_column: request.time_column || 'timestamp',
          target_column: request.target_column || 'latency',
          frequency: request.frequency || 'T',
          level: request.level || [80, 90, 99],
        },
        {
          timeout: 60000, // 60 seconds timeout for processing
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      console.log(`✅ Anomaly detection completed: ${response.data.anomalies_detected}/${response.data.total_points} anomalies detected`);
      return response.data;
    } catch (error: any) {
      console.error('❌ Anomaly detection failed:', error.message);
      
      if (error.response) {
        throw new ApiError(
          `Anomaly detection failed: ${error.response.data?.detail || error.message}`,
          error.response.status
        );
      } else if (error.code === 'ECONNREFUSED') {
        throw new ApiError('Anomaly detection service is not running', 503);
      } else {
        throw new ApiError(`Anomaly detection failed: ${error.message}`, 500);
      }
    }
  }

  /**
   * Generate forecasts for metrics
   */
  async generateForecast(request: ForecastRequest): Promise<ForecastResponse> {
    // Check service health first
    const isHealthy = await this.checkServiceHealth();
    if (!isHealthy) {
      throw new ApiError('Anomaly detection service is not available', 503);
    }

    // Validate input data
    if (!request.data || request.data.length < 10) {
      throw new ApiError('Insufficient data points for forecasting (minimum 10 required)', 400);
    }

    try {
      console.log(`📈 Starting forecast for provider ${request.provider_id} with ${request.data.length} data points`);
      
      const response: AxiosResponse<ForecastResponse> = await axios.post(
        `${this.pythonServiceUrl}/forecast`,
        {
          provider_id: request.provider_id,
          data: request.data,
          horizon: request.horizon || 60,
          time_column: request.time_column || 'timestamp',
          target_column: request.target_column || 'latency',
          frequency: request.frequency || 'T',
        },
        {
          timeout: 60000, // 60 seconds timeout for processing
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      console.log(`✅ Forecast completed: ${response.data.results.length} future points generated`);
      return response.data;
    } catch (error: any) {
      console.error('❌ Forecast generation failed:', error.message);
      
      if (error.response) {
        throw new ApiError(
          `Forecast generation failed: ${error.response.data?.detail || error.message}`,
          error.response.status
        );
      } else if (error.code === 'ECONNREFUSED') {
        throw new ApiError('Anomaly detection service is not running', 503);
      } else {
        throw new ApiError(`Forecast generation failed: ${error.message}`, 500);
      }
    }
  }

  /**
   * Export health check data to CSV format
   */
  async exportToCSV(data: HealthCheckData[], filename: string): Promise<string> {
    try {
      const outputDir = join(process.cwd(), 'data', 'exports');
      await mkdir(outputDir, { recursive: true });
      
      const filePath = join(outputDir, filename);
      
      const csvWriter = createObjectCsvWriter({
        path: filePath,
        header: [
          { id: 'timestamp', title: 'timestamp' },
          { id: 'latency', title: 'latency' },
          { id: 'is_healthy', title: 'is_healthy' },
          { id: 'response_time', title: 'response_time' },
          { id: 'error_rate', title: 'error_rate' },
        ],
      });

      await csvWriter.writeRecords(data);
      console.log(`✅ Data exported to CSV: ${filePath}`);
      
      return filePath;
    } catch (error: any) {
      console.error('❌ CSV export failed:', error.message);
      throw new ApiError(`CSV export failed: ${error.message}`, 500);
    }
  }

  /**
   * Get service status
   */
  async getServiceStatus(): Promise<{
    isHealthy: boolean;
    lastHealthCheck: number;
    serviceUrl: string;
  }> {
    const isHealthy = await this.checkServiceHealth();
    
    return {
      isHealthy,
      lastHealthCheck: this.lastHealthCheck,
      serviceUrl: this.pythonServiceUrl,
    };
  }

  /**
   * Process health check data for anomaly detection
   * Converts from database format to service format
   */
  processHealthCheckData(healthChecks: any[]): HealthCheckData[] {
    return healthChecks.map(check => ({
      timestamp: check.timestamp,
      latency: check.latency,
      is_healthy: check.isHealthy,
      response_time: check.responseTime,
      error_rate: 0, // Calculate from error data if available
    }));
  }
}

// Export singleton instance
export const anomalyDetectionService = new AnomalyDetectionService();
