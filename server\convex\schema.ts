import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
	messages: defineTable({
		body: v.string(),
		title: v.string(),
		author: v.string(),
		authorName: v.string(),
		createdAt: v.number(),
	}).index('by_author', ['author']),

	apiProviders: defineTable({
		name: v.string(),
		type: v.string(),
		endpoint: v.string(),
		isHealthy: v.boolean(),
		latency: v.number(),
		errorRate: v.number(),
		priority: v.number(),
		isPrimary: v.optional(v.boolean()),
		lastCheck: v.string(),
		userId: v.string(),
	}).index('by_user', ['userId']),

	healthChecks: defineTable({
		providerId: v.id('apiProviders'),
		timestamp: v.number(),
		isHealthy: v.boolean(),
		latency: v.number(),
		statusCode: v.optional(v.number()),
		errorMessage: v.optional(v.string()),
		responseTime: v.number(),
	})
		.index('by_provider', ['providerId'])
		.index('by_timestamp', ['timestamp']),

	userProfiles: defineTable({
		userId: v.string(),
		email: v.string(),
		name: v.string(),
		profileImageUrl: v.optional(v.string()),
		createdAt: v.number(),
		lastLoginAt: v.number(),
	}).index('by_userId', ['userId']),

	// AI Anomaly Detection Tables
	anomalyDetectionResults: defineTable({
		providerId: v.id('apiProviders'),
		analysisId: v.string(), // Unique ID for each analysis run
		timestamp: v.number(), // When the analysis was performed
		dataPointTimestamp: v.number(), // Timestamp of the specific data point
		value: v.number(), // The actual metric value (latency, response_time, etc.)
		metricType: v.string(), // 'latency', 'response_time', 'error_rate'
		isAnomaly: v.boolean(),
		anomalyScore: v.number(), // 0-1 scale
		confidenceLevel: v.number(), // 80, 90, 99
		upperBound: v.optional(v.number()),
		lowerBound: v.optional(v.number()),
		severity: v.string(), // 'low', 'medium', 'high'
		userId: v.string(),
	})
		.index('by_provider', ['providerId'])
		.index('by_user', ['userId'])
		.index('by_analysis', ['analysisId'])
		.index('by_timestamp', ['timestamp'])
		.index('by_anomaly', ['isAnomaly']),

	anomalyDetectionAnalyses: defineTable({
		analysisId: v.string(), // Unique ID for the analysis
		providerId: v.id('apiProviders'),
		userId: v.string(),
		startTimestamp: v.number(), // Start of data period analyzed
		endTimestamp: v.number(), // End of data period analyzed
		analysisTimestamp: v.number(), // When the analysis was performed
		metricType: v.string(), // 'latency', 'response_time', 'error_rate'
		totalDataPoints: v.number(),
		anomaliesDetected: v.number(),
		anomalyRate: v.number(), // Percentage
		confidenceLevels: v.array(v.number()), // [80, 90, 99]
		status: v.string(), // 'completed', 'failed', 'in_progress'
		errorMessage: v.optional(v.string()),
		summary: v.object({
			dataQuality: v.object({
				totalPoints: v.number(),
				missingPoints: v.number(),
				dataCompleteness: v.number(),
			}),
			anomalyStatistics: v.object({
				totalAnomalies: v.number(),
				anomalyRatePercent: v.number(),
				severityDistribution: v.object({
					high: v.number(),
					medium: v.number(),
					low: v.number(),
				}),
			}),
		}),
	})
		.index('by_provider', ['providerId'])
		.index('by_user', ['userId'])
		.index('by_analysis_id', ['analysisId'])
		.index('by_timestamp', ['analysisTimestamp']),

	forecastResults: defineTable({
		providerId: v.id('apiProviders'),
		forecastId: v.string(), // Unique ID for each forecast run
		generatedTimestamp: v.number(), // When the forecast was generated
		forecastTimestamp: v.number(), // Future timestamp being predicted
		metricType: v.string(), // 'latency', 'response_time', 'error_rate'
		predictedValue: v.number(),
		confidenceIntervalLower: v.number(),
		confidenceIntervalUpper: v.number(),
		horizon: v.number(), // How many time units ahead
		userId: v.string(),
	})
		.index('by_provider', ['providerId'])
		.index('by_user', ['userId'])
		.index('by_forecast', ['forecastId'])
		.index('by_generated_timestamp', ['generatedTimestamp'])
		.index('by_forecast_timestamp', ['forecastTimestamp']),

	anomalyDetectionSettings: defineTable({
		userId: v.string(),
		providerId: v.optional(v.id('apiProviders')), // null for global settings
		isEnabled: v.boolean(),
		autoAnalysisEnabled: v.boolean(),
		analysisFrequency: v.number(), // Minutes between automatic analyses
		minDataPoints: v.number(),
		confidenceLevels: v.array(v.number()),
		metricTypes: v.array(v.string()), // ['latency', 'response_time', 'error_rate']
		alertThresholds: v.object({
			anomalyRatePercent: v.number(), // Alert if anomaly rate exceeds this
			severityLevel: v.string(), // 'low', 'medium', 'high'
		}),
		retentionDays: v.number(), // How long to keep anomaly data
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		.index('by_user', ['userId'])
		.index('by_provider', ['providerId'])
		.index('by_user_provider', ['userId', 'providerId']),
});
